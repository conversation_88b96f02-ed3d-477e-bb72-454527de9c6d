@echo off
title API Playground - Auto Startup
color 0A

REM Change to the correct directory
cd /d "c:\Users\<USER>\Downloads\apiplayground"

REM Set Java environment
set "JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo ========================================
echo    API Playground - Auto Startup
echo ========================================
echo.
echo [INFO] Starting at system startup...
echo [INFO] Application directory: %CD%
echo.

REM Start Spring Boot in minimized window
echo [INFO] Starting Spring Boot server...
start "API Playground Server" /min cmd /c "title API Playground Server && color 0E && cd /d c:\Users\<USER>\Downloads\apiplayground && set JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot && set PATH=%JAVA_HOME%\bin;%PATH% && .\mvnw.cmd spring-boot:run"

REM Wait for server to start, then open browser
echo [INFO] Waiting for server to start...
echo [INFO] This may take 1-2 minutes on first startup...

REM Wait 90 seconds for server startup
timeout /t 90 /nobreak >nul

echo [INFO] Opening web browser...
start "" "http://localhost:8080"

echo.
echo [SUCCESS] API Playground started!
echo [INFO] Server is running in background (minimized window)
echo [INFO] Web interface: http://localhost:8080
echo.
echo [INFO] To stop the server, close the "API Playground Server" window
echo [INFO] or restart your computer.
echo.
echo This window will close in 10 seconds...
timeout /t 10 /nobreak >nul
