@echo off
title API Playground - Simple Start
color 0A

echo ========================================
echo    API Playground - Simple Start
echo ========================================
echo.

REM Set Java Environment
set "JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo [INFO] Starting Spring Boot application...
echo [INFO] This will take about 30-60 seconds...
echo.

REM Start Spring Boot in a new window
start "Spring Boot Server - Keep This Window Open" cmd /c "title Spring Boot Server && color 0E && echo Starting Spring Boot Application... && echo. && set JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot && set PATH=%JAVA_HOME%\bin;%PATH% && .\mvnw.cmd spring-boot:run && pause"

REM Wait a reasonable time then open browser
echo [INFO] Waiting 45 seconds for server to start...
timeout /t 45 /nobreak

echo [INFO] Opening web browser...
start "" "http://localhost:8080"

echo.
echo [SUCCESS] Startup complete!
echo.
echo [INFO] If the web page doesn't load immediately:
echo         - Wait a bit longer for the server to finish starting
echo         - Check the Spring Boot Server window for "Started ApiplaygroundApplication"
echo         - Refresh the browser page
echo.
echo [INFO] Application URL: http://localhost:8080
echo [INFO] API Endpoints:
echo         - POST http://localhost:8080/api/chat
echo         - POST http://localhost:8080/api/echo
echo.
echo Press any key to exit this window...
pause >nul
