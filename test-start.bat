@echo off
echo Testing Java and Maven...
echo.

REM Set JAVA_HOME explicitly
set "JAVA_HOME=C:\Program Files\Microsoft\jdk-17.0.15.6-hotspot"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo JAVA_HOME set to: %JAVA_HOME%
echo.

echo Testing Java...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Java test failed!
    pause
    exit /b 1
)

echo.
echo Testing Maven wrapper...
.\mvnw.cmd -version
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Maven test failed!
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Java and Maven are working!
echo Starting Spring Boot application...
echo.
.\mvnw.cmd spring-boot:run

pause
