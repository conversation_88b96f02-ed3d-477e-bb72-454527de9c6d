* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f5f5;
    height: 100vh;
    overflow: hidden;
}

.container {
    display: flex;
    height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* Left Panel Styles */
.left-panel {
    width: 400px;
    background: #fafafa;
    border-right: 1px solid #e0e0e0;
    padding: 20px;
    overflow-y: auto;
}

.setup-section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
}

.parameters-section h3 {
    color: #555;
    margin: 25px 0 15px 0;
    font-size: 18px;
    font-weight: 500;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

select.form-control {
    cursor: pointer;
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

/* Slider Styles */
.slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    transition: background 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
    background: #0056b3;
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: none;
}

.slider-value {
    min-width: 50px;
    text-align: center;
    font-weight: 500;
    color: #555;
    font-size: 14px;
}

/* Right Panel Styles */
.right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-section {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chat-section h2 {
    color: #333;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.clear-button {
    padding: 8px 16px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background 0.3s ease;
}

.clear-button:hover {
    background: #c82333;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid #e0e0e0;
}

.welcome-message {
    text-align: center;
    color: #666;
    font-style: italic;
    margin-top: 50px;
}

.message {
    margin-bottom: 15px;
    padding: 12px 16px;
    border-radius: 12px;
    max-width: 80%;
    word-wrap: break-word;
}

.user-message {
    background: #007bff;
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.assistant-message {
    background: white;
    color: #333;
    border: 1px solid #e0e0e0;
    border-bottom-left-radius: 4px;
}

.loading-message {
    background: #f0f0f0;
    color: #666;
    font-style: italic;
    border-bottom-left-radius: 4px;
}

/* Chat Input Styles */
.chat-input-container {
    border-top: 1px solid #e0e0e0;
    padding-top: 20px;
}

.input-group {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 12px;
    resize: none;
    font-size: 14px;
    line-height: 1.4;
    transition: border-color 0.3s ease;
}

.chat-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.send-button {
    padding: 12px 24px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
    white-space: nowrap;
}

.send-button:hover {
    background: #0056b3;
}

.send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }
    
    .left-panel {
        width: 100%;
        height: auto;
        max-height: 40vh;
    }
    
    .right-panel {
        flex: 1;
        min-height: 60vh;
    }
}

/* Scrollbar Styles */
.chat-messages::-webkit-scrollbar,
.left-panel::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.left-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb,
.left-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.left-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
