@echo off
title API Playground - Quick Start
color 0B

echo ========================================
echo    API Playground - Quick Start
echo ========================================
echo.

REM Set Java Environment
set "JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot"
set "PATH=%JAVA_HOME%\bin;%PATH%"

REM Start Spring Boot application in background
echo [INFO] Starting Spring Boot application...
start "Spring Boot Server" cmd /c "set JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot && set PATH=%JAVA_HOME%\bin;%PATH% && .\mvnw.cmd spring-boot:run"

REM Wait for server to start
echo [INFO] Waiting for server to start...
:WAIT_LOOP
timeout /t 3 /nobreak >nul
netstat -an | find "8080" | find "LISTENING" >nul
if %ERRORLEVEL% NEQ 0 (
    echo [INFO] Still starting... Please wait.
    goto WAIT_LOOP
)

REM Open browser when ready
echo [SUCCESS] Server is ready! Opening browser...
start "" "http://localhost:8080"

echo.
echo [SUCCESS] Application started successfully!
echo [INFO] Web interface: http://localhost:8080
echo [INFO] Server is running in background.
echo.
echo Press any key to exit...
pause >nul
