<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Chat Interface</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Left Panel - Setup -->
        <div class="left-panel">
            <div class="setup-section">
                <h2>Setup</h2>
                
                <!-- Model Selection -->
                <div class="form-group">
                    <label for="model-select">Model</label>
                    <select id="model-select" class="form-control">
                        <option value="">Select a model...</option>
                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        <option value="gpt-4">GPT-4</option>
                        <option value="gpt-4-turbo">GPT-4 Turbo</option>
                        <!-- Add more models as needed -->
                    </select>
                </div>

                <!-- Instructions Text Box -->
                <div class="form-group">
                    <label for="instructions">Give the model instructions and context</label>
                    <textarea id="instructions" class="form-control" rows="4" 
                              placeholder="Enter instructions and context for the model..."></textarea>
                </div>

                <!-- Parameters Section -->
                <div class="parameters-section">
                    <h3>Parameters</h3>
                    
                    <!-- Max Tokens -->
                    <div class="form-group">
                        <label for="max-tokens">Max Tokens</label>
                        <div class="slider-container">
                            <input type="range" id="max-tokens" class="slider" min="1" max="4000" value="1000">
                            <span class="slider-value" id="max-tokens-value">1000</span>
                        </div>
                    </div>

                    <!-- Temperature -->
                    <div class="form-group">
                        <label for="temperature">Temperature</label>
                        <div class="slider-container">
                            <input type="range" id="temperature" class="slider" min="0" max="2" step="0.1" value="0.7">
                            <span class="slider-value" id="temperature-value">0.7</span>
                        </div>
                    </div>

                    <!-- Top P -->
                    <div class="form-group">
                        <label for="top-p">Top P</label>
                        <div class="slider-container">
                            <input type="range" id="top-p" class="slider" min="0" max="1" step="0.01" value="1">
                            <span class="slider-value" id="top-p-value">1.00</span>
                        </div>
                    </div>

                    <!-- Presence Penalty -->
                    <div class="form-group">
                        <label for="presence-penalty">Presence Penalty</label>
                        <div class="slider-container">
                            <input type="range" id="presence-penalty" class="slider" min="-2" max="2" step="0.1" value="0">
                            <span class="slider-value" id="presence-penalty-value">0.0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - Chat -->
        <div class="right-panel">
            <div class="chat-section">
                <div class="chat-header">
                    <h2>Chat</h2>
                    <button id="clear-chat" class="clear-button" onclick="clearChat()">Clear Chat</button>
                </div>
                
                <!-- Chat Messages Container -->
                <div class="chat-messages" id="chat-messages">
                    <div class="welcome-message">
                        <p>Welcome! Configure your settings on the left and start chatting.</p>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="chat-input-container">
                    <div class="input-group">
                        <textarea id="user-input" class="chat-input" rows="3" 
                                  placeholder="Type your message here..." 
                                  onkeydown="handleKeyDown(event)"></textarea>
                        <button id="send-button" class="send-button" onclick="sendMessage()">
                            <span>Send</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
