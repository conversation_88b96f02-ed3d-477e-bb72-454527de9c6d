package com.playground.apiplayground;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
public class WebController {

    @GetMapping("/")
    public String index() {
        return "forward:/index.html";
    }

    @GetMapping("/chat")
    public String chat() {
        return "forward:/index.html";
    }

    // REST API endpoint that accepts <PERSON><PERSON>N
    @PostMapping("/api/echo")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> echoJson(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Received your JSON data successfully!");
        response.put("receivedData", requestData);
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    // Another example endpoint for chat-like requests
    @PostMapping("/api/chat")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> chatEndpoint(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> response = new HashMap<>();

        // Extract message from request
        String userMessage = (String) requestData.get("message");
        if (userMessage == null) {
            userMessage = "No message provided";
        }

        response.put("status", "success");
        response.put("userMessage", userMessage);
        response.put("botResponse", "Hello! You said: " + userMessage + ". This is a simple echo response.");
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }
}
