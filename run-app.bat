@echo off
title API Playground - Starting Application
color 0A

echo ========================================
echo    API Playground Application Startup
echo ========================================
echo.

REM Set Java Environment
set "JAVA_HOME=C:\Program Files\Microsoft\jdk-17.0.15.6-hotspot"
set "PATH=%JAVA_HOME%\bin;%PATH%"
echo [INFO] JAVA_HOME is set to: %JAVA_HOME%

REM Test Java installation
echo [INFO] Testing Java installation...
java -version
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Java not found! Please check JAVA_HOME path.
    pause
    exit /b 1
)
echo.

REM Clean and compile project
echo [INFO] Cleaning and compiling project...
.\mvnw.cmd clean compile
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Compilation failed!
    pause
    exit /b 1
)
echo.

REM Start Spring Boot application in background and open browser
echo [INFO] Starting Spring Boot application...
echo [INFO] Please wait for the application to start...
echo [INFO] The browser will open automatically when ready.
echo.

REM Start Spring Boot in background
start "Spring Boot Server" cmd /c ".\mvnw.cmd spring-boot:run"

REM Wait for server to start (check if port 8080 is available)
echo [INFO] Waiting for server to start on port 8080...
:WAIT_LOOP
timeout /t 2 /nobreak >nul
netstat -an | find "8080" | find "LISTENING" >nul
if %ERRORLEVEL% NEQ 0 (
    echo [INFO] Still starting... Please wait.
    goto WAIT_LOOP
)

REM Server is ready, open browser
echo [SUCCESS] Server is ready!
echo [INFO] Opening web browser...
start "" "http://localhost:8080"

REM Open additional tools (optional)
echo.
echo [INFO] Application started successfully!
echo [INFO] Web interface: http://localhost:8080
echo [INFO] API endpoints:
echo         - POST http://localhost:8080/api/chat
echo         - POST http://localhost:8080/api/echo
echo.
echo [INFO] Press any key to open additional development tools...
pause >nul

REM Open additional development tools
echo [INFO] Opening development tools...

REM Open API testing tool (if curl is available)
where curl >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [INFO] curl is available for API testing
) else (
    echo [INFO] Consider installing curl for API testing
)

REM Open PowerShell for API testing
echo [INFO] Opening PowerShell for API testing...
start "PowerShell - API Testing" powershell -NoExit -Command "Write-Host 'API Testing PowerShell Window' -ForegroundColor Green; Write-Host 'Test the API with:' -ForegroundColor Yellow; Write-Host '$headers = @{ \"Content-Type\" = \"application/json\" }' -ForegroundColor Cyan; Write-Host '$body = @{ message = \"Hello API!\" } | ConvertTo-Json' -ForegroundColor Cyan; Write-Host 'Invoke-RestMethod -Uri \"http://localhost:8080/api/chat\" -Method POST -Body $body -Headers $headers' -ForegroundColor Cyan; Write-Host ''; Write-Host 'Application URL: http://localhost:8080' -ForegroundColor Magenta"

echo.
echo [SUCCESS] All components started successfully!
echo [INFO] Keep this window open to see server logs.
echo [INFO] Close this window to stop the application.
echo.
pause
