// API Configuration
const API_URL = 'https://eduhk-api-ea.azure-api.net/chatgpt-uat';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeSliders();
    setupEventListeners();
});

// Initialize slider value displays
function initializeSliders() {
    const sliders = [
        { id: 'max-tokens', valueId: 'max-tokens-value' },
        { id: 'temperature', valueId: 'temperature-value' },
        { id: 'top-p', valueId: 'top-p-value' },
        { id: 'presence-penalty', valueId: 'presence-penalty-value' }
    ];

    sliders.forEach(slider => {
        const element = document.getElementById(slider.id);
        const valueElement = document.getElementById(slider.valueId);
        
        element.addEventListener('input', function() {
            let value = this.value;
            if (slider.id === 'temperature' || slider.id === 'top-p' || slider.id === 'presence-penalty') {
                value = parseFloat(value).toFixed(slider.id === 'top-p' ? 2 : 1);
            }
            valueElement.textContent = value;
        });
    });
}

// Setup event listeners
function setupEventListeners() {
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');
    
    // Auto-resize textarea
    userInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
    });
}

// Handle Enter key in textarea
function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

// Send message to API
async function sendMessage() {
    const userInput = document.getElementById('user-input');
    const message = userInput.value.trim();
    
    if (!message) {
        return;
    }

    // Disable send button and clear input
    const sendButton = document.getElementById('send-button');
    sendButton.disabled = true;
    userInput.value = '';
    userInput.style.height = 'auto';

    // Add user message to chat
    addMessageToChat(message, 'user');

    // Show loading message
    const loadingId = addLoadingMessage();

    try {
        // Prepare API request - adjust format based on your API requirements
        const requestData = {
            messages: [
                {
                    role: "system",
                    content: document.getElementById('instructions').value || "You are a helpful assistant."
                },
                {
                    role: "user",
                    content: message
                }
            ],
            model: document.getElementById('model-select').value || 'gpt-3.5-turbo',
            max_tokens: parseInt(document.getElementById('max-tokens').value),
            temperature: parseFloat(document.getElementById('temperature').value),
            top_p: parseFloat(document.getElementById('top-p').value),
            presence_penalty: parseFloat(document.getElementById('presence-penalty').value)
        };

        // Make API call
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        // Remove loading message
        removeLoadingMessage(loadingId);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        // Extract response text (adjust based on actual API response format)
        let responseText = '';
        if (data.choices && data.choices[0] && data.choices[0].message) {
            responseText = data.choices[0].message.content;
        } else if (data.response) {
            responseText = data.response;
        } else if (data.message) {
            responseText = data.message;
        } else if (typeof data === 'string') {
            responseText = data;
        } else {
            responseText = 'Received response from API';
        }

        // Add assistant response to chat
        addMessageToChat(responseText, 'assistant');

    } catch (error) {
        console.error('Error calling API:', error);
        
        // Remove loading message
        removeLoadingMessage(loadingId);
        
        // Show error message
        let errorMessage = 'Sorry, there was an error processing your request. ';
        if (error.message.includes('Failed to fetch')) {
            errorMessage += 'Please check your internet connection and try again.';
        } else if (error.message.includes('HTTP error')) {
            errorMessage += `Server responded with error: ${error.message}`;
        } else {
            errorMessage += 'Please try again later.';
        }
        
        addMessageToChat(errorMessage, 'error');
    } finally {
        // Re-enable send button
        sendButton.disabled = false;
        userInput.focus();
    }
}

// Add message to chat display
function addMessageToChat(message, type) {
    const chatMessages = document.getElementById('chat-messages');
    const welcomeMessage = chatMessages.querySelector('.welcome-message');
    
    // Remove welcome message if it exists
    if (welcomeMessage) {
        welcomeMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}-message`;
    
    // Format message content
    const messageContent = document.createElement('div');
    messageContent.textContent = message;
    messageDiv.appendChild(messageContent);

    // Add timestamp for assistant messages
    if (type === 'assistant' || type === 'error') {
        const timestamp = document.createElement('div');
        timestamp.className = 'timestamp';
        timestamp.style.fontSize = '12px';
        timestamp.style.color = '#666';
        timestamp.style.marginTop = '5px';
        timestamp.textContent = new Date().toLocaleTimeString();
        messageDiv.appendChild(timestamp);
    }

    chatMessages.appendChild(messageDiv);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Add loading message
function addLoadingMessage() {
    const chatMessages = document.getElementById('chat-messages');
    const loadingDiv = document.createElement('div');
    const loadingId = 'loading-' + Date.now();
    
    loadingDiv.id = loadingId;
    loadingDiv.className = 'message loading-message';
    loadingDiv.innerHTML = '<div>Thinking...</div>';
    
    chatMessages.appendChild(loadingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    return loadingId;
}

// Remove loading message
function removeLoadingMessage(loadingId) {
    const loadingElement = document.getElementById(loadingId);
    if (loadingElement) {
        loadingElement.remove();
    }
}

// Clear chat history
function clearChat() {
    const chatMessages = document.getElementById('chat-messages');
    chatMessages.innerHTML = '<div class="welcome-message"><p>Chat cleared. Start a new conversation!</p></div>';
}

// Export settings (optional feature)
function exportSettings() {
    const settings = {
        model: document.getElementById('model-select').value,
        instructions: document.getElementById('instructions').value,
        maxTokens: document.getElementById('max-tokens').value,
        temperature: document.getElementById('temperature').value,
        topP: document.getElementById('top-p').value,
        presencePenalty: document.getElementById('presence-penalty').value
    };
    
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = 'chat-settings.json';
    link.click();
}
